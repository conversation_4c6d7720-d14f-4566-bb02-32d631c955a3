import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../context/AuthContext';
import shiftService from '../../services/shiftService';
import toast from 'react-hot-toast';
import {
  classifyShiftWithBusinessRules,
  getShiftDisplayConfig,
  validateShiftTimes,
  formatTimeDisplay,
  getShiftDurationDisplay
} from '../../utils/shift-classification';

const ShiftManagement = () => {
  // Authentication hook (no destructuring needed for now)
  useAuth();
  const [shifts, setShifts] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingShift, setEditingShift] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filters, setFilters] = useState({
    truck_id: '',
    driver_id: '',
    status: '',
    shift_type: ''
  });

  // Load shifts function with useCallback to prevent infinite re-renders
  const loadShifts = useCallback(async () => {
    try {
      setLoading(true);
      const queryParams = {
        date_from: selectedDate,
        date_to: selectedDate,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      };
      console.log('SHIFT_MANAGEMENT_LOAD', 'Loading shifts with params:', queryParams);

      const response = await shiftService.getShifts(queryParams);
      console.log('SHIFT_MANAGEMENT_RESPONSE', 'Received shifts:', {
        count: response.data?.length || 0,
        shifts: response.data?.map(s => ({ id: s.id, truck: s.truck_number, status: s.status })) || []
      });

      setShifts(response.data || []);
    } catch (error) {
      console.error('Error loading shifts:', error);
      toast.error('Failed to load shifts');
    } finally {
      setLoading(false);
    }
  }, [selectedDate, filters]);

  // Load initial data
  useEffect(() => {
    loadShifts();
    loadTrucks();
    loadDrivers();
  }, [loadShifts]);

  // Remove duplicate loadShifts function since it's now defined above with useCallback

  const loadTrucks = async () => {
    try {
      const response = await fetch('/api/trucks', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setTrucks(data.data || []);
      }
    } catch (error) {
      console.error('Error loading trucks:', error);
    }
  };

  const loadDrivers = async () => {
    try {
      const response = await fetch('/api/drivers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setDrivers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading drivers:', error);
    }
  };

  const handleActivateShift = async (shiftId) => {
    try {
      await shiftService.activateShift(shiftId);
      toast.success('Shift activated successfully');
      loadShifts();
    } catch (error) {
      console.error('Error activating shift:', error);
      toast.error('Failed to activate shift');
    }
  };

  const handleCancelShift = async (shiftId) => {
    if (window.confirm('Are you sure you want to cancel this shift?')) {
      try {
        await shiftService.cancelShift(shiftId);
        toast.success('Shift cancelled successfully');
        loadShifts();
      } catch (error) {
        console.error('Error cancelling shift:', error);
        toast.error('Failed to cancel shift');
      }
    }
  };

  const handleEditShift = (shift) => {
    // Set the shift data for editing
    setEditingShift(shift);
    setShowCreateModal(true);
  };

  const handleDeleteShift = async (shiftId) => {
    if (window.confirm('Are you sure you want to permanently delete this shift? This action cannot be undone.')) {
      try {
        const response = await shiftService.deleteShift(shiftId);

        // Handle different response types based on backend action
        if (response.action === 'cancelled') {
          toast.warning(response.message || 'Shift cancelled instead of deleted due to associated trip logs');
        } else if (response.data && response.data.cancelled_shifts && response.data.cancelled_shifts.length > 0) {
          const cancelledCount = response.data.cancelled_shifts.length;
          const deletedCount = response.data.deleted_shifts.length;
          if (cancelledCount > 0 && deletedCount > 0) {
            toast.warning(`${deletedCount} shifts deleted, ${cancelledCount} shifts cancelled (have associated trips)`);
          } else if (cancelledCount > 0) {
            toast.warning(`${cancelledCount} shifts cancelled instead of deleted (have associated trip logs)`);
          } else {
            toast.success(response.message || 'Shifts processed successfully');
          }
        } else {
          toast.success(response.message || 'Shift deleted successfully');
        }

        loadShifts();
      } catch (error) {
        console.error('Error deleting shift:', error);
        toast.error('Failed to delete shift');
      }
    }
  };

  const getStatusColor = (status) => {
    return shiftService.getShiftStatusColor(status);
  };

  const getShiftTypeDisplay = (shiftType) => {
    return shiftService.getShiftTypeDisplay(shiftType);
  };

  const formatTime = (timeString) => {
    return shiftService.formatShiftTime(timeString);
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-secondary-900">
                🔄 Shift Management
              </h1>
              <p className="mt-2 text-secondary-600">
                Manage driver shifts and schedules for multi-driver truck operations
              </p>
            </div>
            <a
              href="/shifts/integration"
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm"
            >
              📋 View Assignment Integration
            </a>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Date
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => {
                  console.log('SHIFT_MANAGEMENT_DATE_CHANGE', 'Date changed to:', e.target.value);
                  setSelectedDate(e.target.value);
                }}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Truck
              </label>
              <select
                value={filters.truck_id}
                onChange={(e) => {
                  console.log('SHIFT_MANAGEMENT_TRUCK_FILTER', 'Truck filter changed to:', e.target.value);
                  setFilters(prev => ({ ...prev, truck_id: e.target.value }));
                }}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Trucks</option>
                {trucks.map(truck => (
                  <option key={truck.id} value={truck.id}>
                    {truck.truck_number}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Driver
              </label>
              <select
                value={filters.driver_id}
                onChange={(e) => setFilters(prev => ({ ...prev, driver_id: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Drivers</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="scheduled">Scheduled</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Shift Type
              </label>
              <select
                value={filters.shift_type}
                onChange={(e) => setFilters(prev => ({ ...prev, shift_type: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Types</option>
                <option value="day">Day Shift</option>
                <option value="night">Night Shift</option>
                <option value="custom">Custom Shift</option>
              </select>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => {
                  console.log('MANUAL_REFRESH', 'Manual refresh triggered');
                  loadShifts();
                }}
                disabled={loading}
                className="bg-secondary-600 text-white px-4 py-2 rounded-md hover:bg-secondary-700 transition-colors disabled:opacity-50 flex items-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Refreshing...
                  </>
                ) : (
                  <>🔄 Refresh</>
                )}
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Create Shift
              </button>
            </div>
          </div>
        </div>

        {/* Shifts Table */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
          <div className="px-6 py-4 bg-secondary-50 border-b border-secondary-200">
            <h3 className="text-lg font-medium text-secondary-900">
              Shifts for {new Date(selectedDate).toLocaleDateString()}
            </h3>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-secondary-500">Loading shifts...</p>
            </div>
          ) : shifts.length === 0 ? (
            <div className="p-8 text-center">
              <span className="text-4xl block mb-2">📅</span>
              <p className="text-secondary-500">No shifts found for the selected criteria</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Truck & Driver
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Shift Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Assignment
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {shifts.map((shift) => (
                    <tr key={shift.id} className="hover:bg-secondary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {shift.truck_number}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {shift.driver_name} • {shift.employee_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {getShiftTypeDisplay(shift.shift_type)}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {shift.start_date === shift.end_date
                              ? new Date(shift.start_date).toLocaleDateString()
                              : `${new Date(shift.start_date).toLocaleDateString()} - ${new Date(shift.end_date).toLocaleDateString()}`
                            }
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {formatTime(shift.start_time)} - {formatTime(shift.end_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(shift.status)}`}>
                          {shiftService.getShiftStatusDisplay(shift.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {shift.assignment_code || 'No Assignment'}
                        </div>
                        {shift.assignment_status && (
                          <div className="text-xs text-secondary-500">
                            Status: {shift.assignment_status}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {/* Edit Button */}
                          {shift.status !== 'completed' && shift.status !== 'cancelled' && (
                            <button
                              onClick={() => handleEditShift(shift)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Edit Shift"
                            >
                              ✏️ Edit
                            </button>
                          )}

                          {/* Activate Button */}
                          {shift.status === 'scheduled' && (
                            <button
                              onClick={() => handleActivateShift(shift.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Activate Shift"
                            >
                              ▶️ Activate
                            </button>
                          )}

                          {/* Cancel Button */}
                          {shift.status !== 'completed' && shift.status !== 'cancelled' && (
                            <button
                              onClick={() => handleCancelShift(shift.id)}
                              className="text-orange-600 hover:text-orange-900"
                              title="Cancel Shift"
                            >
                              ⏸️ Cancel
                            </button>
                          )}

                          {/* Delete Button */}
                          {(shift.status === 'cancelled' || shift.status === 'scheduled') && (
                            <button
                              onClick={() => handleDeleteShift(shift.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete Shift"
                            >
                              🗑️ Delete
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Shift Modal */}
      {showCreateModal && (
        <CreateShiftModal
          trucks={trucks}
          drivers={drivers}
          selectedDate={selectedDate}
          editingShift={editingShift}
          onClose={() => {
            setShowCreateModal(false);
            setEditingShift(null);
          }}
          onSuccess={() => {
            console.log('SHIFT_MODAL_SUCCESS', 'Modal success callback triggered');
            setShowCreateModal(false);
            setEditingShift(null);

            // Force immediate data refresh
            loadShifts().then(() => {
              console.log('SHIFT_DATA_REFRESHED', 'Shift data refreshed after modal success');
            }).catch(error => {
              console.error('SHIFT_REFRESH_ERROR', 'Error refreshing shift data:', error);
            });
          }}
        />
      )}
    </div>
  );
};

// Complete Create/Edit Shift Modal component
const CreateShiftModal = ({ trucks, drivers, selectedDate, editingShift, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    truck_id: editingShift?.truck_id || '',
    driver_id: editingShift?.driver_id || '',
    shift_type: editingShift?.shift_type || 'day',
    start_date: editingShift?.start_date || selectedDate,
    end_date: editingShift?.end_date || selectedDate,
    start_time: editingShift?.start_time?.substring(0, 5) || '06:00',
    end_time: editingShift?.end_time?.substring(0, 5) || '18:00',
    handover_notes: editingShift?.handover_notes || '',
    // Enhanced fields for date range scheduling
    recurrence_pattern: editingShift?.recurrence_pattern || 'single',
    start_date: editingShift?.start_date || selectedDate,
    end_date: editingShift?.end_date || selectedDate
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [shiftClassification, setShiftClassification] = useState(null);

  // Predefined shift templates
  const shiftTemplates = {
    day: { start_time: '06:00', end_time: '18:00' },
    night: { start_time: '18:00', end_time: '06:00' },
    custom: { start_time: '08:00', end_time: '16:00' }
  };

  // Recurrence pattern options
  const recurrenceOptions = [
    { value: 'single', label: 'Single Date', description: 'One-time shift for specific date' },
    { value: 'daily', label: 'Daily', description: 'Every day within date range' },
    { value: 'weekly', label: 'Weekly', description: 'Same day of week within date range' },
    { value: 'weekdays', label: 'Weekdays Only', description: 'Monday to Friday only' },
    { value: 'weekends', label: 'Weekends Only', description: 'Saturday and Sunday only' },
    { value: 'custom', label: 'Custom Pattern', description: 'Custom recurrence logic' }
  ];

  // Update shift classification when times change
  useEffect(() => {
    if (formData.start_time && formData.end_time) {
      const validation = validateShiftTimes(formData.start_time, formData.end_time);
      if (validation.isValid) {
        const classification = classifyShiftWithBusinessRules(
          formData.start_time,
          formData.end_time,
          formData.shift_type
        );
        setShiftClassification({
          ...classification,
          validation,
          displayConfig: getShiftDisplayConfig(classification.shiftType, classification.displayType),
          durationDisplay: getShiftDurationDisplay(formData.start_time, formData.end_time)
        });
      } else {
        setShiftClassification({
          classification: 'invalid',
          validation,
          displayConfig: getShiftDisplayConfig('custom'),
          durationDisplay: 'Invalid'
        });
      }
    }
  }, [formData.start_time, formData.end_time, formData.shift_type]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = {
        ...prev,
        [name]: value
      };

      // Handle recurrence pattern changes - unified approach
      if (name === 'recurrence_pattern') {
        if (value === 'single') {
          // Single date - ensure start_date = end_date
          if (!newData.start_date) {
            newData.start_date = selectedDate;
          }
          newData.end_date = newData.start_date;
        } else {
          // Date range - ensure both dates are set
          if (!newData.start_date) {
            newData.start_date = selectedDate;
          }
          if (!newData.end_date) {
            newData.end_date = selectedDate;
          }
        }
      }

      return newData;
    });

    // Auto-fill times when shift type changes
    if (name === 'shift_type' && shiftTemplates[value]) {
      setFormData(prev => ({
        ...prev,
        start_time: shiftTemplates[value].start_time,
        end_time: shiftTemplates[value].end_time
      }));
    }

    // Clear errors when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.truck_id) newErrors.truck_id = 'Please select a truck';
    if (!formData.driver_id) newErrors.driver_id = 'Please select a driver';
    if (!formData.start_time) newErrors.start_time = 'Start time is required';
    if (!formData.end_time) newErrors.end_time = 'End time is required';

    // Check if start and end times are different
    if (formData.start_time === formData.end_time) {
      newErrors.end_time = 'End time must be different from start time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Convert time format for API (HH:MM to HH:MM:SS)
      const shiftData = {
        ...formData,
        start_time: `${formData.start_time}:00`,
        end_time: `${formData.end_time}:00`,
        truck_id: parseInt(formData.truck_id),
        driver_id: parseInt(formData.driver_id),
        // Add intelligent classification data
        display_type: shiftClassification?.displayType || formData.shift_type
      };

      // Unified approach - always use start_date and end_date
      if (formData.recurrence_pattern === 'single') {
        // Single date shift - start_date = end_date
        shiftData.start_date = formData.start_date;
        shiftData.end_date = formData.start_date; // Same date for single day
        console.log('SINGLE_DATE_SHIFT', 'Using unified approach', {
          recurrence_pattern: formData.recurrence_pattern,
          start_date: formData.start_date,
          end_date: formData.start_date
        });
      } else {
        // Date range shift - use start_date and end_date
        shiftData.start_date = formData.start_date;
        shiftData.end_date = formData.end_date;
        console.log('DATE_RANGE_SHIFT', 'Using date range mode', {
          recurrence_pattern: formData.recurrence_pattern,
          start_date: formData.start_date,
          end_date: formData.end_date
        });
      }

      console.log('SHIFT_DATA_PREPARED', 'Final shift data being sent to API', shiftData);

      if (editingShift) {
        // Update existing shift
        const updateResult = await shiftService.updateShift(editingShift.id, shiftData);
        toast.success('Shift updated successfully!');

        // Log successful update for debugging
        console.log('SHIFT_UPDATE_SUCCESS', 'Shift updated successfully', {
          shift_id: editingShift.id,
          updated_data: shiftData,
          result: updateResult
        });
      } else {
        // Create new shift
        const createResult = await shiftService.createShift(shiftData);
        toast.success('Shift created successfully!');

        // Log successful creation for debugging
        console.log('SHIFT_CREATE_SUCCESS', 'Shift created successfully', {
          created_data: shiftData,
          result: createResult
        });
      }

      // Ensure parent component refreshes data
      onSuccess();
    } catch (error) {
      console.error(`Error ${editingShift ? 'updating' : 'creating'} shift:`, error);
      toast.error(error.message || `Failed to ${editingShift ? 'update' : 'create'} shift`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-secondary-900">
            {editingShift ? 'Edit Shift' : 'Create New Shift'}
          </h3>
          <button
            onClick={onClose}
            className="text-secondary-400 hover:text-secondary-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Truck Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Truck *
            </label>
            <select
              name="truck_id"
              value={formData.truck_id}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.truck_id ? 'border-red-300' : 'border-secondary-300'
              }`}
            >
              <option value="">Select a truck</option>
              {trucks.map(truck => (
                <option key={truck.id} value={truck.id}>
                  {truck.truck_number} - {truck.license_plate}
                </option>
              ))}
            </select>
            {errors.truck_id && (
              <p className="text-red-500 text-xs mt-1">{errors.truck_id}</p>
            )}
          </div>

          {/* Driver Selection */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Driver *
            </label>
            <select
              name="driver_id"
              value={formData.driver_id}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.driver_id ? 'border-red-300' : 'border-secondary-300'
              }`}
            >
              <option value="">Select a driver</option>
              {drivers.map(driver => (
                <option key={driver.id} value={driver.id}>
                  {driver.full_name} - {driver.employee_id}
                </option>
              ))}
            </select>
            {errors.driver_id && (
              <p className="text-red-500 text-xs mt-1">{errors.driver_id}</p>
            )}
          </div>

          {/* Shift Type with Intelligent Classification */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Shift Type
            </label>
            <select
              name="shift_type"
              value={formData.shift_type}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-secondary-300 rounded-md"
            >
              <option value="day">Day Shift (6 AM - 6 PM)</option>
              <option value="night">Night Shift (6 PM - 6 AM)</option>
              <option value="custom">Custom Shift</option>
            </select>
            {shiftClassification && shiftClassification.classification !== 'invalid' && (
              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center text-sm">
                  <span className="mr-2">{shiftClassification.displayConfig.icon}</span>
                  <span className="font-medium text-blue-800">
                    Intelligent Classification: {shiftClassification.displayConfig.label}
                  </span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  {shiftClassification.reason}
                </p>
              </div>
            )}
          </div>

          {/* Recurrence Pattern */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Schedule Type
            </label>
            <select
              name="recurrence_pattern"
              value={formData.recurrence_pattern}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-secondary-300 rounded-md"
            >
              {recurrenceOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="text-xs text-secondary-500 mt-1">
              {recurrenceOptions.find(opt => opt.value === formData.recurrence_pattern)?.description}
            </p>
          </div>

          {/* Date Fields - Single or Range */}
          {formData.recurrence_pattern === 'single' ? (
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Shift Date
              </label>
              <input
                type="date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              />
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  onChange={handleInputChange}
                  min={formData.start_date}
                  className="w-full px-3 py-2 border border-secondary-300 rounded-md"
                />
              </div>
            </div>
          )}

          {/* Time Range */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Start Time *
              </label>
              <input
                type="time"
                name="start_time"
                value={formData.start_time}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.start_time ? 'border-red-300' : 'border-secondary-300'
                }`}
              />
              {errors.start_time && (
                <p className="text-red-500 text-xs mt-1">{errors.start_time}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                End Time *
              </label>
              <input
                type="time"
                name="end_time"
                value={formData.end_time}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.end_time ? 'border-red-300' : 'border-secondary-300'
                }`}
              />
              {errors.end_time && (
                <p className="text-red-500 text-xs mt-1">{errors.end_time}</p>
              )}
            </div>
          </div>

          {/* Shift Duration and Validation */}
          {shiftClassification && (
            <div>
              {shiftClassification.classification === 'invalid' ? (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center text-sm text-red-800">
                    <span className="mr-2">⚠️</span>
                    <span className="font-medium">Invalid Shift Times</span>
                  </div>
                  <ul className="text-xs text-red-600 mt-1 ml-6 list-disc">
                    {shiftClassification.validation.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center text-green-800">
                      <span className="mr-2">⏱️</span>
                      <span className="font-medium">Duration: {shiftClassification.durationDisplay}</span>
                    </div>
                    <div className="flex items-center text-green-600">
                      <span className="mr-1">
                        {formatTimeDisplay(formData.start_time)} - {formatTimeDisplay(formData.end_time)}
                      </span>
                      {shiftClassification.validation.isOvernight && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          Overnight
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Handover Notes */}
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Notes (Optional)
            </label>
            <textarea
              name="handover_notes"
              value={formData.handover_notes}
              onChange={handleInputChange}
              rows={3}
              placeholder="Any special instructions or notes for this shift..."
              className="w-full px-3 py-2 border border-secondary-300 rounded-md"
            />
          </div>

          {/* Assignment Integration Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              📋 Assignment Integration
            </h4>
            <p className="text-sm text-blue-700">
              When this shift becomes active, the system will:
            </p>
            <ul className="text-sm text-blue-700 mt-1 ml-4 list-disc">
              <li>Automatically create shift-aware assignments</li>
              <li>Preserve existing assignments for this truck</li>
              <li>Enable seamless handovers during active trips</li>
              <li>Track performance per driver-shift combination</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                'Create Shift'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ShiftManagement;
