const jwt = require('jsonwebtoken');
const https = require('https');
const http = require('http');

async function testShiftsAPI() {
  try {
    // Generate a test JWT token with correct payload structure
    const token = jwt.sign(
      {
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          full_name: 'System Administrator',
          role: 'admin'
        }
      },
      'hauling_qr_jwt_secret_2025_secure_key_for_development',
      { expiresIn: '24h' }
    );

    console.log('Testing Shifts API...');
    console.log('Token generated:', token.substring(0, 50) + '...');

    // Test the shifts API endpoint
    const postData = JSON.stringify({});
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/shifts',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const response = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            ok: res.statusCode >= 200 && res.statusCode < 300,
            headers: res.headers,
            json: () => Promise.resolve(JSON.parse(data)),
            text: () => Promise.resolve(data)
          });
        });
      });
      req.on('error', reject);
      req.end();
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    if (response.ok) {
      const data = await response.json();
      console.log('API Response:');
      console.log('- Success:', data.success);
      console.log('- Total records returned:', data.data ? data.data.length : 0);
      console.log('- Pagination:', data.pagination);
      
      if (data.data && data.data.length > 0) {
        console.log('\nShift records returned:');
        data.data.forEach((shift, index) => {
          console.log(`${index + 1}. ID: ${shift.id}, Truck: ${shift.truck_number}, Driver: ${shift.driver_name}, Status: ${shift.status}, Type: ${shift.shift_type}`);
        });
      }
    } else {
      const errorText = await response.text();
      console.error('API Error:', errorText);
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testShiftsAPI();
