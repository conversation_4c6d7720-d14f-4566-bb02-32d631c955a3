const jwt = require('jsonwebtoken');
const https = require('https');
const http = require('http');

async function testShiftsAPI() {
  try {
    // Generate a test JWT token with correct payload structure
    const token = jwt.sign(
      {
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          full_name: 'System Administrator',
          role: 'admin'
        }
      },
      'hauling_qr_jwt_secret_2025_secure_key_for_development',
      { expiresIn: '24h' }
    );

    console.log('Testing Shifts API...');
    console.log('Token generated:', token.substring(0, 50) + '...');

    // Helper functions for date ranges (same as SimplifiedShiftManagement)
    const getWeekStart = () => {
      const today = new Date();
      const day = today.getDay();
      const diff = today.getDate() - day + (day === 0 ? -6 : 1);
      const monday = new Date(today.setDate(diff));
      return monday.toISOString().split('T')[0];
    };

    const getWeekEnd = () => {
      const today = new Date();
      const day = today.getDay();
      const diff = today.getDate() - day + (day === 0 ? 0 : 7);
      const sunday = new Date(today.setDate(diff));
      return sunday.toISOString().split('T')[0];
    };

    const weekStart = getWeekStart();
    const weekEnd = getWeekEnd();

    // Test multiple filter scenarios
    const testCases = [
      {
        name: 'No filters (all shifts)',
        path: '/api/shifts'
      },
      {
        name: 'Today only (ShiftManagement default)',
        path: `/api/shifts?date_from=2025-07-19&date_to=2025-07-19`
      },
      {
        name: `Current week (SimplifiedShiftManagement default): ${weekStart} to ${weekEnd}`,
        path: `/api/shifts?date_from=${weekStart}&date_to=${weekEnd}`
      },
      {
        name: 'Status filter: active',
        path: `/api/shifts?status=active`
      },
      {
        name: 'Shift type filter: day',
        path: `/api/shifts?shift_type=day`
      },
      {
        name: 'Truck filter: truck_id=1',
        path: `/api/shifts?truck_id=1`
      },
      {
        name: 'Driver filter: driver_id=1',
        path: `/api/shifts?driver_id=1`
      },
      {
        name: 'Combined filters: active + day',
        path: `/api/shifts?status=active&shift_type=day`
      },
      {
        name: 'Test SimplifiedShiftManagement filters: truck_id=1',
        path: `/api/shifts?truck_id=1&date_from=${weekStart}&date_to=${weekEnd}`
      },
      {
        name: 'Test ShiftManagement filters: truck_id=1 + today',
        path: `/api/shifts?truck_id=1&date_from=2025-07-19&date_to=2025-07-19`
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n=== ${testCase.name} ===`);

      const options = {
        hostname: 'localhost',
        port: 5000,
        path: testCase.path,
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      const response = await new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => {
            data += chunk;
          });
          res.on('end', () => {
            resolve({
              status: res.statusCode,
              ok: res.statusCode >= 200 && res.statusCode < 300,
              headers: res.headers,
              json: () => Promise.resolve(JSON.parse(data)),
              text: () => Promise.resolve(data)
            });
          });
        });
        req.on('error', reject);
        req.end();
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('- Total records returned:', data.data ? data.data.length : 0);

        if (data.data && data.data.length > 0) {
          console.log('Records:');
          data.data.forEach((shift, index) => {
            console.log(`  ${index + 1}. ID: ${shift.id}, Truck: ${shift.truck_number}, Driver: ${shift.driver_name}, Status: ${shift.status}, Type: ${shift.shift_type}`);
          });
        } else {
          console.log('No records found');
        }
      } else {
        const errorText = await response.text();
        console.error('API Error:', errorText);
      }
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testShiftsAPI();
